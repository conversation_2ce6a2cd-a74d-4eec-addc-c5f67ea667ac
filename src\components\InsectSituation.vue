<template>
    <div class="InsectSituation">
        <div class="InsectSituation_left">
            <!-- 智慧物联 -->
            <div class="InsectSituation_left_smartIOTData">
                <div class="title InsectSituation_left_smartIOTData_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>智慧物联数据</span>
                </div>
                <div class="InsectSituation_left_smartIOTData_con">
                    <div class="handleBox">
                        <div class="handleBox_item formStyle">
                            <el-select
                                v-model="areaId"
                                @change="areaChange"
                                clearable
                                placeholder="请选择区域"
                                popper-class="selectStyle_list"
                            >
                                <el-option
                                    v-for="item in areaIdData"
                                    :key="item.areaId"
                                    :label="item.areaName"
                                    :value="item.areaId"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="list_title list_title1">
                        <div>总危害指数：</div>
                        <div v-if="wormCaseData.harm==null">0</div>
                        <div v-else>{{ wormCaseData.harm }}</div>
                        <div>
                            <el-tooltip popper-class="tooltipWidthStyle" placement="bottom">
                                <div slot="content">
                                    <div class="item">
                                        1级：0.0—0.4 2级：0.5-0.9 3级：1.0—1.4 4级：1.5-2.0 5级：≥2.0
                                    </div>
                                    <br />
                                    <div class="item">
                                        1—5级
                                    </div>
                                    <br />
                                    <div class="item">
                                        1：受害作物产量和质量基本无影响
                                        2：对作物有一定的危害，但程度较轻,对产量和质量的影响基本在允许范围之内
                                        3:对作物已产生明显危害，产量损失3-5%之间，作物品质也受到影响
                                        4:作物将受到严重危害，产量损失在5-15%之间﹐作物品质明显下降
                                        5:作物受害非常严重，产量损失在15-30%以上，局部可发生绝产。
                                    </div>
                                </div>
                                <div>
                                    <img src="../assets/image/centralControlPlatform/doubt-tip1.png" alt="" />
                                </div>
                            </el-tooltip>
                            <!-- <img src="../assets/image/centralControlPlatform/doubt-tip1.png" alt="" /> -->
                        </div>
                    </div>
                    <div class="list_con1">
                        <div class="list_item">
                            <div v-if="wormCaseData.density==null">0</div>
                            <div v-else>{{ wormCaseData.density }}</div>
                            <div>近日虫情密度</div>
                        </div>
                        <div class="list_item">
                            <div v-if="wormCaseData.kind==null">0</div>
                            <div v-else>{{ wormCaseData.kind }}</div>
                            <div>近日虫情种类</div>
                        </div>
                        <div class="list_item">
                            <div v-if="wormCaseData.grandTotal==null">0</div>
                            <div v-else>{{ wormCaseData.grandTotal }}</div>
                            <div>累计监测虫情</div>
                        </div>
                    </div>
                    <div class="list_title list_title2">
                        <div>
                            近日监测虫情：
                            <span v-if="list.length == 0">无，虫情防治优</span>
                        </div>
                    </div>
                    <div class="list_con2" v-if="list">
                        <div id="scroll_box">
                            <vue-seamless-scroll
                                :data="list"
                                :class-option="classOption"
                                class="seamless-warp"
                                style="width: 100%"
                            >
                                <div id="scroll1">
                                    <div class="list_item" v-for="(item, index) in list" :key="index">
                                        <div class="item">{{ item.kind | kindFormat }}</div>
                                        <div class="item">{{ item.density }}只/亩</div>
                                        <div class="item">危害指数{{ item.harm }}</div>
                                    </div>
                                </div>
                            </vue-seamless-scroll>
                        </div>
                    </div>
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
            <!-- 设备报警 -->
            <div class="InsectSituation_left_equipmentAlarm">
                <div class="title InsectSituation_left_equipmentAlarm_title">
                    <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                    <span>虫情报警</span>
                </div>
                <div class="InsectSituation_left_equipmentAlarm_con">
                    <EquipmentAlarm :type="4" :alarmCollect="alarmCollect" />
                </div>
                <div class="bottom_left">
                    <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                </div>
                <div class="bottom_right">
                    <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                </div>
            </div>
        </div>
        <div class="InsectSituation_right">
            <div class="title InsectSituation_right_title">
                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                <span>智慧农业示范区地图</span>
            </div>
            <div class="InsectSituation_right_btn" @click="floatingWindow">
                <EquipmentButton />
            </div>
            <!-- <div class="InsectSituation_right_con" @click="InsectSituationEMDialogOpen"> -->
            <div class="InsectSituation_right_con">
                <znyAMap :type="4"></znyAMap>
                <!-- <div class="twoDimensionalMap" @click="InsectSituationEMDialogOpen"></div> -->
            </div>
        </div>

        <!-- 报警记录弹窗 -->
        <AlarmDialog />
    </div>
</template>
<script>
// import $ from 'jquery';
import znyAMap from '../components/znyAMap.vue'
import EquipmentAlarm from '../components/equipmentAlarm.vue'
import EquipmentButton from '../components/equipmentButton.vue'
import AlarmDialog from '../components/Dialog/AlarmDialog.vue'
import WormCaseService from '../jaxrs/concrete/com.zny.ia.api.WormCaseService.js'
import CommonService from '../jaxrs/concrete/com.zny.ia.api.CommonService'
export default {
    components: {
        EquipmentAlarm,
        EquipmentButton,
        znyAMap,
        AlarmDialog,
    },
    data() {
        return {
            areaId: null,
            areaIdData: [],
            wormCaseData: {}, //智慧物联数据
            list: [],
            classOption: {
                step: 0.5,
            },
            alarmCollect: {}, //报警信息
        }
    },
    mounted() {
        this.getAreaData()
        this.getWormCaseData()
        this.getListAlarm()
    },
    methods: {
        //获取区域数据
        getAreaData() {
            CommonService.allAreaListInformation().then(res => {
                this.areaIdData = res
            })
        },
        //区域改变
        areaChange(val) {
            this.areaId = val
            this.getWormCaseData()
        },
        // 获取虫情数据
        getWormCaseData() {
            WormCaseService.wormCaseData(this.areaId).then(res => {
                if(JSON.stringify(res) == "{}"){
                    this.wormCaseData={}
                    this.list=[]
                }else{
                    this.wormCaseData = res
                    this.list = res.wormCaseMonitor
                }
            })
        },
        // 获取土壤报警
        getListAlarm() {
            WormCaseService.alarmCollect().then(res => {
                this.alarmCollect = res
            })
        },
        floatingWindow() {
            this.$router.push('/floatingWindow')
        },
        // // 打开虫情环境监测弹窗
        // InsectSituationEMDialogOpen() {
        //     this.zEmit('InsectSituationEMDialogOpen')
        // },
    },
    beforeDestroy() {
        const that = this
        that.Scroll && clearInterval(that.Scroll)
        that.timer && clearInterval(that.timer)
        Object.assign(that.$data, that.$options.data())
    },
}
</script>
<style lang="less">
@import '../assets/css/meteorology.less';
@import '../assets/css/InsectSituation.less';
</style>
