<template>
    <div>
        <div>
            <div class="centralControlPlatform">

                <div class="centralControlPlatform_con">
                    <!-- 三列布局 -->
                    <div class="centralControlPlatform_left">
                        <!-- 智慧物联数据 -->
                        <div class="centralControlPlatform_smartIOTData">
                            <div class="title centralControlPlatform_smartIOTData_title">
                                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                                <span>智慧物联数据</span>
                            </div>
                            <el-button class="historyButton formStyle" @click="openSmartIOTHistoryDialog"><span>历史数据</span></el-button>
                            <div class="centralControlPlatform_smartIOTData_con">
                                <div class="centralControlPlatform_smartIOTData_con_row">
                                    <div class="centralControlPlatform_smartIOTData_con_row_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/icon1.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.temperature==null">--</div>
                                            <div v-else>{{smartIOTData.temperature}}℃</div>
                                            <div>温度</div>
                                        </div>
                                    </div>
                                    <div class="centralControlPlatform_smartIOTData_con_row_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/icon2.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.humidity==null">--</div>
                                            <div v-else>{{smartIOTData.humidity}}%RH</div>
                                            <div>湿度</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="centralControlPlatform_smartIOTData_con_row2">
                                    <div class="centralControlPlatform_smartIOTData_con_row2_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/co2.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.co2==null">--</div>
                                            <div v-else>{{smartIOTData.co2}}ppm</div>
                                            <div>二氧化碳含量</div>
                                        </div>
                                    </div>
                                    <div class="centralControlPlatform_smartIOTData_con_row2_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/lx.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.lightIntensity==null">--</div>
                                            <div v-else>{{smartIOTData.lightIntensity}}lx</div>
                                            <div>光照强度</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="centralControlPlatform_smartIOTData_con_row3">
                                    <div class="centralControlPlatform_smartIOTData_con_row3_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/tuwen.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.soilTemperature==null">--</div>
                                            <div v-else>{{smartIOTData.soilTemperature}}℃</div>
                                            <div>土温</div>
                                        </div>
                                    </div>
                                    <div class="centralControlPlatform_smartIOTData_con_row3_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/tushi.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.soilHumidity==null">--</div>
                                            <div v-else>{{smartIOTData.soilHumidity}}%</div>
                                            <div>土湿</div>
                                        </div>
                                    </div>
                                    <div class="centralControlPlatform_smartIOTData_con_row3_item">
                                        <div class="item_img">
                                            <img src="../assets/image/monitoringCenter/diandao.png" alt="" />
                                        </div>
                                        <div class="item_text">
                                            <div v-if="smartIOTData.conductivity==null">--</div>
                                            <div v-else>{{smartIOTData.conductivity}}μS/cm</div>
                                            <div>电导率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bottom_left">
                                <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                            </div>
                            <div class="bottom_right">
                                <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                            </div>
                        </div>
                        <!-- 设备报警 -->
                        <div class="centralControlPlatform_equipmentAlarm">
                            <div class="title centralControlPlatform_equipmentAlarm_title">
                                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                                <span>警情报警</span>
                            </div>
                            <div class="centralControlPlatform_equipmentAlarm_con">
                                <EquipmentAlarm :type='0' :alarmCollect='alarmCollect'/>
                            </div>
                            <div class="bottom_left">
                                <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                            </div>
                            <div class="bottom_right">
                                <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                            </div>
                        </div>
                    </div>
                    <div class="centralControlPlatform_center">
                        <!-- 智慧农业示范区地图 -->
                        <div class="centralControlPlatform_demonstrationMap">
                            <div class="title centralControlPlatform_demonstrationMap_title">
                                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                                <span>智能日光温室大棚示范基地</span>
                            </div>
                            <div class="centralControlPlatform_demonstrationMap_btn">
                                <EquipmentButton />
                            </div>
                            <div class="centralControlPlatform_demonstrationMap_con">
                                <znyAMap :type="1"></znyAMap>
                            </div>
                        </div>
                    </div>
                    <div class="centralControlPlatform_right">
                        <!-- 苗情监测 -->
                        <div class="centralControlPlatform_seedlingMonitoring">
                            <div class="title centralControlPlatform_seedlingMonitoring_title">
                                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                                <span>苗情监测</span>
                            </div>
                            <div class="centralControlPlatform_seedlingMonitoring_con">
                               <!-- <NewSeedlingMonitoring/> -->
                                <SeedlingMonitoring/>
                            </div>
                            <div class="bottom_left">
                                <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                            </div>
                            <div class="bottom_right">
                                <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                            </div>
                        </div>
                        <!-- 气象报警 -->
                        <div class="centralControlPlatform_meteorologyAlarm">
                            <div class="title centralControlPlatform_meteorologyAlarm_title">
                                <img src="../assets/image/monitoringCenter/title_icon.png" alt="" />
                                <span>设备操作</span>
                            </div>
                            <div class="centralControlPlatform_meteorologyAlarm_con">
                                <DeviceOperation :deviceData="deviceOperationData" />
                            </div>
                            <div class="bottom_left">
                                <img src="../assets/image/monitoringCenter/bottom_left.png" alt="" />
                            </div>
                            <div class="bottom_right">
                                <img src="../assets/image/monitoringCenter/bottom_right.png" alt="" />
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>

        <!-- 智慧物联历史数据弹窗 -->
        <SmartIOTHistoryDialog />

        <!-- 历史记录弹窗 -->
        <meteorologyOfHistoryDialog />

        <!-- 汇报更新弹窗 -->
        <meteorologyReportUpdateDialog />
        <soilReportUpdateDialog />

        <!-- 报警设备侧边栏 -->
        <AlarmEquipmentSidebar />

        <!-- 报警记录弹窗 -->
        <AlarmDialog />
    </div>
</template>
<script>
import znyAMap from '../components/znyAMap.vue'
import EquipmentAlarm from '../components/equipmentAlarm.vue'
import EquipmentButton from '../components/equipmentButton.vue'
import NewSeedlingMonitoring from '../components/NewSeedlingMonitoring.vue'
import DeviceOperation from '../components/DeviceOperation.vue'
import SmartIOTHistoryDialog from '../components/Dialog/SmartIOTHistoryDialog.vue'
import meteorologyOfHistoryDialog from '../components/Dialog/meteorologyOfHistoryDialog.vue'
import meteorologyReportUpdateDialog from '../components/Dialog/meteorologyReportUpdateDialog.vue'
import soilReportUpdateDialog from '../components/Dialog/soilReportUpdateDialog.vue'
import AlarmDialog from '../components/Dialog/AlarmDialog.vue'
import AlarmEquipmentSidebar from '../components/AlarmEquipmentSidebar.vue'
import SeedlingMonitoring from '../components/SeedlingMonitoring.vue'

import HomePageService from '../jaxrs/concrete/com.zny.ia.api.HomePageService.js'
import WeatherService from '../jaxrs/concrete/com.zny.ia.api.WeatherService.js'

export default {
    name: 'centralControlPlatform',
    components: {
        EquipmentAlarm,
        EquipmentButton,
        znyAMap,
        SeedlingMonitoring,
        NewSeedlingMonitoring,
        DeviceOperation,
        SmartIOTHistoryDialog,
        meteorologyOfHistoryDialog,
        meteorologyReportUpdateDialog,
        soilReportUpdateDialog,
        AlarmDialog,
        AlarmEquipmentSidebar,
    },
    data() {
        return {
            menuList: [],
            smartIOTData: {}, //智能物联数据
            alarmCollect: {}, //报警信息
            deviceOperationData: {}, //设备操作数据
            floatingWindowboole: false,
            currentAreaId: '', //当前选中的地块ID
            currentAreaName: '', //当前选中的地块名称
            collectDateTime: '', //数据采集时间
        }
    },
    created(){
        // this.getMenuList()
    },
    mounted() {

        
        // 监听地块点击事件，接收智慧物联数据
        this.$root.$on('plot-iot-data', (iotData) => {
            console.log('中控平台接收到智慧物联数据:', iotData);
            this.smartIOTData = iotData;
            this.collectDateTime = iotData.createTime || '';
        });

        // 监听地块选择事件，接收地块信息
        this.$root.$on('area-selection-change', (areaData) => {
            console.log('中控平台接收到地块选择数据:', areaData);
            this.currentAreaId = areaData.areaId || '';
            this.currentAreaName = areaData.areaName || '';
        });

        // 监听地块点击事件，接收报警数据
        this.$root.$on('plot-alarm-data', (alarmData) => {
            console.log('中控平台接收到报警数据:', alarmData);
            this.alarmCollect = alarmData;
        });

        // 监听地块点击事件，接收设备操作数据
        this.$root.$on('plot-device-operation-data', (deviceOperationData) => {
            console.log('中控平台接收到设备操作数据:', deviceOperationData);
            this.deviceOperationData = deviceOperationData;
        });
    },
    methods: {
        // 获取权限列表
        // getMenuList(){
        //     this.menuList=JSON.parse(localStorage.getItem('frontPermissionVoList'))
        // },
        // floatingWindow() {
        //     this.$router.push('/floatingWindow')
        // },
        // 打开智慧物联历史数据弹窗
        openSmartIOTHistoryDialog() {
            this.zEmit('smartIOTHistoryDialogOpen', {
                areaId: this.currentAreaId || '1',
                areaName: this.currentAreaName || '暂无数据',
                smartIOTData: this.smartIOTData,
                collectDateTime: this.collectDateTime
            })
        },
    },
    beforeDestroy() {
        const that = this
        Object.assign(that.$data, that.$options.data())
       // 移除事件监听
       this.$root.$off('plot-iot-data');
        this.$root.$off('plot-alarm-data');
        this.$root.$off('plot-device-operation-data');
        this.$root.$off('area-selection-change');
    },
}
</script>
<style lang="less">
@import '../assets/css/centralControlPlatformNew.less';
</style>
